import math

"""
--- 自定义信号 ---

可以在此文件中添加新的信号类型，程序自动在界面中显示。

1. 创建一个新的信号生成函数：
   - 函数必须接收两个参数：`params` (一个包含其所需参数的字典) 和 `t` (当前时间戳)。
   - 函数应返回一个数值 (int 或 float)。
   - 示例：
     def generate_ramp(params, t):
         slope = params.get('斜率', 1)
         return slope * (t % 10) # 一个简单的锯齿波

2. 在下面的两个字典中注册新信号：
   - `SIGNAL_GENERATORS`: 
     - 'key' 想在下拉菜单中显示的名称 (例如 "斜坡信号")。
     - 'value' 刚刚创建的函数名 (例如 generate_ramp)。

   - `SIGNAL_PARAM_DEFINITIONS`:
     - 'key' 必须与 `SIGNAL_GENERATORS` 中的 key 完全相同。
     - 'value' 是一个字典，定义了该信号需要哪些参数，以及它们的默认值。
       这些参数将自动显示在UI界面上。

完成这两步后，重启发送端程序，新的 "斜坡信号" 就会出现在下拉菜单中。
"""


# --- 信号生成函数定义 ---

def generate_sine(params, t):
    """生成正弦波"""
    amplitude = params.get('振幅', 1.0)
    frequency = params.get('频率(Hz)', 0.1)
    phase = params.get('相位', 0.0)
    offset = params.get('偏置', 0.0)
    return offset + amplitude * math.sin(2 * math.pi * frequency * t + phase)


def generate_step(params, t):
    """生成阶跃信号 (方波)"""
    high_level = params.get('高电平', 1000)
    low_level = params.get('低电平', -1000)
    period = params.get('周期(s)', 2.0)
    # 周期的一半以上是高电平，否则是低电平
    if (t % period) > (period / 2):
        return high_level
    else:
        return low_level


def generate_constant(params, t):
    """生成固定值"""
    value = params.get('数值', 0)
    return value


def generate_attenuation(params, t):
    """
    生成指数衰减信号，用于平滑归零。
    t 在这里是从归零开始计时的时间。
    """
    start_value = params.get('start_value', 0)
    decay_rate = params.get('decay_rate', 5.0)  # k值，越大衰减越快

    val = start_value * math.exp(-decay_rate * t)

    # 当值足够接近0时，直接返回0，避免无限计算
    if abs(val) < 1:
        return 0
    return val

def none(params, t):
    """生成固定值"""
    value = params.get('数值', 0)
    return value

# --- 1. 信号生成器注册表 ---
# 将信号名称映射到它们的生成函数
SIGNAL_GENERATORS = {
    "正弦波": generate_sine,
    "阶跃信号": generate_step,
    "固定值": generate_constant,
    # 如果您添加了 generate_ramp, 在这里加上:
    # "斜坡信号": generate_ramp,
    # 新增：内部使用的衰减信号，不直接在UI下拉框中显示
    "_平滑衰减": generate_attenuation,
    "不发送": none,
}

# --- 2. 信号参数定义注册表 ---
# 定义每种信号类型需要哪些参数，以及它们的默认值
SIGNAL_PARAM_DEFINITIONS = {
    "正弦波": {
        "振幅": 10000.0,
        "频率(Hz)": 0.5,
        "偏置": 0.0,
    },
    "阶跃信号": {
        "高电平": 5000.0,
        "低电平": -5000.0,
        "周期(s)": 4.0,
    },
    "固定值": {
        "数值": 0.0,
    },
    # 如果添加了 "斜坡信号", 在这里加上它的参数:
    # "斜坡信号": {
    #     "斜率": 100.0,
    # },

    # 不发送信号的参数
    "不发送": {
        "数值": 0.0,
    },

    # 衰减信号的参数由程序在运行时动态设置
    "_平滑衰减": {
        "start_value": 0.0,
        "decay_rate": 5.0,
    }

}

# 新增：一个用户可见的信号类型列表，用于填充UI下拉框
# 把内部使用的 "_平滑衰减" 过滤掉
USER_SELECTABLE_SIGNALS = [key for key in SIGNAL_GENERATORS if not key.startswith('_')]