import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
import os

# 导入你已经重命名的两个应用文件
# 假设它们现在叫做 sender_app.py 和 receiver_app.py
import sender_app
import receiver_app


# --- 修改后的GUI类，适配Frame容器 ---
class ModifiedSenderGUI(sender_app.SenderGUI):
    def __init__(self, master):
        # 保存原始的master
        self.master = master
        # 不调用父类的__init__，而是手动初始化需要的部分
        self.sender = sender_app.Protocol422Sender()
        self.status_var = tk.StringVar(value="就绪")
        self.command_widgets = {}
        self.plot_data = {}
        self.plot_history_size = 100

        self.create_widgets()
        self.update_port_list()
        self._create_all_panels()
        self._init_data_table()

        self.master.after(50, self.update_plots)


class ModifiedReceiverGUI(receiver_app.ReceiverGUI):
    def __init__(self, master):
        # 保存原始的master
        self.master = master
        # 不调用父类的__init__，而是手动初始化需要的部分
        self.receiver = receiver_app.Protocol422Receiver()
        self.status_var = tk.StringVar(value="就绪")
        self.data_vars = {}
        # 修复：使用新的matplotlib colormap API
        try:
            self.colors = plt.colormaps.get_cmap('tab20')
        except AttributeError:
            # 兼容旧版本matplotlib
            self.colors = plt.cm.get_cmap('tab20', 20)
        self.display_mode_var = tk.StringVar(value="合并视图")
        self.plot_widgets = {}

        # !! 新增：用于存储表格行ID的字典 !!
        self.table_item_ids = {}

        self.create_widgets_modified()  # 使用修改后的方法
        self.update_port_list()
        self.master.after(100, self.update_loop)

    def create_widgets_modified(self):
        """修改后的create_widgets方法，跳过菜单栏创建"""
        # 跳过菜单栏创建，直接创建主框架
        main_frame = ttk.Frame(self.master, padding=5);
        main_frame.pack(fill=tk.BOTH, expand=True);
        control_frame = ttk.Frame(main_frame);
        control_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(control_frame, text="串口:").pack(side=tk.LEFT, padx=2);
        self.port_combobox = ttk.Combobox(control_frame, width=10, state="readonly");
        self.port_combobox.pack(side=tk.LEFT, padx=2);
        ttk.Label(control_frame, text="波特率:").pack(side=tk.LEFT, padx=2);
        self.baudrate_combobox = ttk.Combobox(control_frame, values=[str(b) for b in receiver_app.BAUDRATES], width=10,
                                              state="readonly");
        self.baudrate_combobox.pack(side=tk.LEFT, padx=2);
        self.baudrate_combobox.set("19200");
        ttk.Button(control_frame, text="刷新", command=self.update_port_list).pack(side=tk.LEFT, padx=2);
        self.start_button = ttk.Button(control_frame, text="启动接收", command=self.start_receiver);
        self.start_button.pack(side=tk.LEFT, padx=(10, 2));
        self.stop_button = ttk.Button(control_frame, text="停止接收", command=self.stop_receiver, state=tk.DISABLED);
        self.stop_button.pack(side=tk.LEFT, padx=2);
        self.clear_button = ttk.Button(control_frame, text="清除数据", command=self.clear_data);
        self.clear_button.pack(side=tk.LEFT, padx=2);

        # 添加协议编辑器按钮到控制面板
        ttk.Button(control_frame, text="协议编辑器", command=self.open_protocol_editor).pack(side=tk.LEFT, padx=(10, 2));

        # 继续复制原始create_widgets方法的其余部分...
        # 显示模式选择
        ttk.Label(control_frame, text="显示:").pack(side=tk.LEFT, padx=(10, 2))
        self.display_mode_combo = ttk.Combobox(control_frame, textvariable=self.display_mode_var,
                                               values=["合并视图", "分离视图"], width=10, state="readonly")
        self.display_mode_combo.pack(side=tk.LEFT, padx=2)
        self.display_mode_combo.bind("<<ComboboxSelected>>", lambda e: self._rebuild_plot_area())

        # 新增：时间窗口设置
        ttk.Label(control_frame, text="时间窗口:").pack(side=tk.LEFT, padx=(10, 2))
        self.time_window_var = tk.StringVar(value="30")
        self.time_window_entry = ttk.Entry(control_frame, textvariable=self.time_window_var, width=6)
        self.time_window_entry.pack(side=tk.LEFT, padx=2)
        ttk.Label(control_frame, text="秒").pack(side=tk.LEFT)
        ttk.Button(control_frame, text="应用", command=self.apply_time_window).pack(side=tk.LEFT, padx=2)
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL);
        paned_window.pack(fill=tk.BOTH, expand=True);
        left_panel = ttk.Frame(paned_window, width=300);
        paned_window.add(left_panel, weight=1);
        right_panel = ttk.Frame(paned_window);
        paned_window.add(right_panel, weight=4)
        select_frame = ttk.LabelFrame(left_panel, text="绘图数据选择");
        select_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2);
        select_canvas = tk.Canvas(select_frame);
        scrollbar = ttk.Scrollbar(select_frame, orient="vertical", command=select_canvas.yview);
        self.checkbox_frame = ttk.Frame(select_canvas);
        self.checkbox_frame.bind("<Configure>",
                                 lambda e: select_canvas.configure(scrollregion=select_canvas.bbox("all")));
        select_canvas.create_window((0, 0), window=self.checkbox_frame, anchor="nw");
        select_canvas.configure(yscrollcommand=scrollbar.set);
        select_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True);
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        table_frame = ttk.LabelFrame(left_panel, text="当前帧数据");
        table_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2);
        columns = ("name", "value", "hex");
        self.data_table = ttk.Treeview(table_frame, columns=columns, show="headings", height=10);
        self.data_table.heading("name", text="字段");
        self.data_table.column("name", width=120);
        self.data_table.heading("value", text="数值");
        self.data_table.column("value", width=60);
        self.data_table.heading("hex", text="十六进制");
        self.data_table.column("hex", width=80);
        self.data_table.pack(fill=tk.BOTH, expand=True)
        self.plot_canvas = tk.Canvas(right_panel);
        plot_scrollbar = ttk.Scrollbar(right_panel, orient="vertical", command=self.plot_canvas.yview);
        self.plot_scroll_frame = ttk.Frame(self.plot_canvas);
        self.plot_scroll_frame.bind("<Configure>",
                                    lambda e: self.plot_canvas.configure(scrollregion=self.plot_canvas.bbox("all")));
        self.plot_canvas.create_window((0, 0), window=self.plot_scroll_frame, anchor="nw");
        self.plot_canvas.configure(yscrollcommand=plot_scrollbar.set);
        self.plot_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True);
        plot_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        status_bar = ttk.Frame(self.master, relief=tk.SUNKEN, padding="2 5");
        status_bar.pack(side=tk.BOTTOM, fill=tk.X);
        ttk.Label(status_bar, textvariable=self.status_var).pack(side=tk.LEFT);
        self.protocol_file_var = tk.StringVar(
            value=os.path.basename(receiver_app.DEFAULT_PROTOCOL_FILE) if os.path.exists(receiver_app.DEFAULT_PROTOCOL_FILE) else "未加载");
        ttk.Label(status_bar, textvariable=self.protocol_file_var).pack(side=tk.RIGHT);
        ttk.Label(status_bar, text="当前协议: ").pack(side=tk.RIGHT)

        self.rebuild_ui_from_protocol()


# --- 主应用程序类 ---
class CombinedApp:
    def __init__(self, master):
        self.master = master
        self.master.title("422通讯收发一体化控制台")
        # 设置一个足够大的默认窗口尺寸以容纳两个界面
        self.master.geometry("2000x850")

        # 使用PanedWindow创建一个可拖拽分割的窗口
        self.paned_window = ttk.PanedWindow(self.master, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # --- 创建左侧面板 (发送端) ---
        # 创建一个Frame作为发送端GUI的容器
        sender_frame = ttk.Frame(self.paned_window, width=900)
        self.paned_window.add(sender_frame, weight=1)  # weight参数控制缩放比例

        # --- 创建右侧面板 (接收端) ---
        # 创建一个Frame作为接收端GUI的容器
        receiver_frame = ttk.Frame(self.paned_window, width=1100)
        self.paned_window.add(receiver_frame, weight=1)

        # --- 实例化两个GUI应用 ---
        # 使用修改后的GUI类，它们不会尝试设置title和geometry
        print("正在初始化发送端UI...")
        self.sender_ui = ModifiedSenderGUI(master=sender_frame)
        print("发送端UI初始化完成。")

        print("正在初始化接收端UI...")
        self.receiver_ui = ModifiedReceiverGUI(master=receiver_frame)
        print("接收端UI初始化完成。")

        # 绑定主窗口的关闭事件
        self.master.protocol("WM_DELETE_WINDOW", self.on_main_closing)

    def on_main_closing(self):
        """
        自定义关闭逻辑，以确保两个应用的后台线程和资源都被正确释放。
        这里我们不直接调用 sender_ui.on_closing() 和 receiver_ui.on_closing()，
        因为它们内部会弹出自己的确认框并试图销毁自己的master Frame，
        这会导致不好的用户体验（弹出多个确认框）和潜在的冲突。

        所以，我们在这里统一处理关闭逻辑。
        """
        if messagebox.askokcancel("退出", "确定要退出整个应用程序吗? 这将停止所有发送和接收任务。"):
            try:
                # 1. 安全地关闭发送端
                print("正在关闭发送端...")
                # 调用其内部的逻辑来停止发送线程和断开串口连接
                self.sender_ui.sender.stop_sending()
                self.sender_ui.sender.disconnect()
                # 手动关闭其创建的matplotlib图形，防止内存泄漏
                for widgets in self.sender_ui.command_widgets.values():
                    if 'fig' in widgets and widgets['fig']:
                        plt.close(widgets['fig'])
                print("发送端已关闭。")

            except Exception as e:
                print(f"关闭发送端时发生错误: {e}")

            try:
                # 2. 安全地关闭接收端
                print("正在关闭接收端...")
                # 调用其内部的逻辑来停止接收线程
                self.receiver_ui.receiver.stop()
                # 手动关闭其创建的matplotlib图形
                for widgets in self.receiver_ui.plot_widgets.values():
                    if 'fig' in widgets and widgets['fig']:
                        plt.close(widgets['fig'])
                print("接收端已关闭。")

            except Exception as e:
                print(f"关闭接收端时发生错误: {e}")

            # 3. 销毁主窗口
            self.master.destroy()


def main():
    """主函数"""
    root = tk.Tk()
    app = CombinedApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()