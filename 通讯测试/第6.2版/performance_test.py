"""
绘图性能测试脚本
比较优化前后的绘图性能
"""
import time
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from optimized_plotting import OptimizedPlotWidget, OptimizedCombinedPlot


class PerformanceTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("绘图性能测试")
        self.root.geometry("1200x800")
        
        # 测试数据
        self.data_size = 1000
        self.num_lines = 5
        self.update_count = 100
        
        # 生成测试数据
        self.generate_test_data()
        
        # 创建测试界面
        self.create_test_interface()
    
    def generate_test_data(self):
        """生成测试数据"""
        self.time_data = np.linspace(0, 10, self.data_size)
        self.test_data = {}
        
        for i in range(self.num_lines):
            # 生成不同频率的正弦波
            freq = 0.5 + i * 0.3
            self.test_data[f'信号{i+1}'] = np.sin(2 * np.pi * freq * self.time_data) + np.random.normal(0, 0.1, self.data_size)
    
    def create_test_interface(self):
        """创建测试界面"""
        # 创建主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        control_frame = tk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Button(control_frame, text="测试传统matplotlib", 
                 command=self.test_traditional_matplotlib, 
                 bg='lightcoral').pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="测试优化版本", 
                 command=self.test_optimized_version,
                 bg='lightgreen').pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="清除结果", 
                 command=self.clear_results).pack(side=tk.LEFT, padx=5)
        
        # 结果显示
        self.result_text = tk.Text(main_frame, height=8, width=80)
        self.result_text.pack(fill=tk.X, pady=(0, 10))
        
        # 绘图区域
        self.plot_frame = tk.Frame(main_frame)
        self.plot_frame.pack(fill=tk.BOTH, expand=True)
    
    def clear_plot_area(self):
        """清除绘图区域"""
        for widget in self.plot_frame.winfo_children():
            widget.destroy()
    
    def test_traditional_matplotlib(self):
        """测试传统matplotlib绘图性能"""
        self.clear_plot_area()
        self.result_text.insert(tk.END, "开始测试传统matplotlib绘图...\n")
        self.root.update()
        
        # 创建传统matplotlib图形
        fig, ax = plt.subplots(figsize=(10, 6))
        canvas = FigureCanvasTkAgg(fig, master=self.plot_frame)
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 初始绘制
        lines = []
        colors = plt.colormaps.get_cmap('tab10')
        for i, (name, data) in enumerate(self.test_data.items()):
            line, = ax.plot(self.time_data, data, label=name, color=colors(i))
            lines.append(line)
        
        ax.legend()
        ax.grid(True)
        ax.set_xlabel("时间")
        ax.set_ylabel("数值")
        ax.set_title("传统matplotlib绘图")
        canvas.draw()
        
        # 性能测试
        start_time = time.time()
        
        for update_idx in range(self.update_count):
            # 模拟数据更新
            ax.clear()
            for i, (name, data) in enumerate(self.test_data.items()):
                # 添加一些随机噪声模拟数据变化
                noisy_data = data + np.random.normal(0, 0.05, len(data))
                ax.plot(self.time_data, noisy_data, label=name, color=colors(i))
            
            ax.legend()
            ax.grid(True)
            ax.set_xlabel("时间")
            ax.set_ylabel("数值")
            ax.set_title(f"传统matplotlib绘图 - 更新 {update_idx+1}/{self.update_count}")
            canvas.draw()
            
            # 更新进度
            if update_idx % 10 == 0:
                self.root.update()
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / self.update_count
        fps = 1.0 / avg_time if avg_time > 0 else 0
        
        result = f"传统matplotlib测试完成:\n"
        result += f"  总时间: {total_time:.3f}秒\n"
        result += f"  平均每次更新: {avg_time*1000:.2f}毫秒\n"
        result += f"  等效FPS: {fps:.1f}\n\n"
        
        self.result_text.insert(tk.END, result)
        self.result_text.see(tk.END)
    
    def test_optimized_version(self):
        """测试优化版本绘图性能"""
        self.clear_plot_area()
        self.result_text.insert(tk.END, "开始测试优化版本绘图...\n")
        self.root.update()
        
        # 创建优化版本图形
        colors = plt.colormaps.get_cmap('tab10')
        field_names = list(self.test_data.keys())
        
        optimized_plot = OptimizedCombinedPlot(
            self.plot_frame, 
            field_names, 
            colors, 
            figsize=(10, 6)
        )
        
        # 性能测试
        start_time = time.time()
        
        for update_idx in range(self.update_count):
            # 模拟数据更新
            updated_data = {}
            for name, data in self.test_data.items():
                # 添加一些随机噪声模拟数据变化
                updated_data[name] = data + np.random.normal(0, 0.05, len(data))
            
            # 使用优化的更新方法
            optimized_plot.update_data(updated_data, self.time_data, 10.0)
            
            # 更新标题
            optimized_plot.plot_widget.ax.set_title(f"优化版本绘图 - 更新 {update_idx+1}/{self.update_count}")
            
            # 更新进度
            if update_idx % 10 == 0:
                self.root.update()
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / self.update_count
        fps = 1.0 / avg_time if avg_time > 0 else 0
        
        result = f"优化版本测试完成:\n"
        result += f"  总时间: {total_time:.3f}秒\n"
        result += f"  平均每次更新: {avg_time*1000:.2f}毫秒\n"
        result += f"  等效FPS: {fps:.1f}\n\n"
        
        self.result_text.insert(tk.END, result)
        self.result_text.see(tk.END)
    
    def clear_results(self):
        """清除结果"""
        self.result_text.delete(1.0, tk.END)
        self.clear_plot_area()
    
    def run(self):
        """运行测试"""
        self.root.mainloop()


if __name__ == "__main__":
    test = PerformanceTest()
    test.run()
