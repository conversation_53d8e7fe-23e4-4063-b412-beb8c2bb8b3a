"""
高效绘图优化模块
使用简化的matplotlib优化技术来提高绘图性能
避免复杂的blitting技术，专注于减少不必要的重绘
"""
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from collections import deque


class OptimizedPlotWidget:
    """优化的绘图组件，使用line对象更新而不是重绘整个图形"""

    def __init__(self, master, figsize=(8, 6), dpi=100):
        self.master = master
        self.fig, self.ax = plt.subplots(figsize=figsize, dpi=dpi)
        self.canvas = FigureCanvasTkAgg(self.fig, master=master)
        self.canvas.get_tk_widget().pack(fill='both', expand=True)

        # 存储line对象，避免重复创建
        self.lines = {}
        self.colors = plt.colormaps.get_cmap('tab20')

        # 设置图形属性
        self.ax.grid(True, alpha=0.3)
        self.ax.set_xlabel("时间 (秒)")
        self.ax.set_ylabel("数值")

        # 标记是否需要重新创建图例
        self.legend_created = False

        # 初始化画布
        self.canvas.draw()
    
    def add_line(self, name, color_index=0):
        """添加一条新的绘图线"""
        if name not in self.lines:
            color = self.colors(color_index % 20)
            line, = self.ax.plot([], [], label=name, color=color, 
                               linewidth=1.5, marker='o', markersize=2, alpha=0.8)
            self.lines[name] = line
            return True
        return False
    
    def update_line_data(self, name, x_data, y_data):
        """更新指定线条的数据"""
        if name in self.lines:
            self.lines[name].set_data(x_data, y_data)
    
    def set_xlim(self, x_min, x_max):
        """设置X轴范围"""
        self.ax.set_xlim(x_min, x_max)
    
    def set_ylim_auto(self):
        """自动设置Y轴范围"""
        self.ax.relim()
        self.ax.autoscale_view(scalex=False, scaley=True)
    
    def update_legend(self):
        """更新图例（仅在需要时）"""
        if not self.legend_created and self.lines:
            self.ax.legend(loc='upper left', fontsize='small')
            self.legend_created = True
            # 重新获取背景
            self.canvas.draw()
            self.background = self.canvas.copy_from_bbox(self.ax.bbox)
    
    def draw_optimized(self):
        """使用优化绘制方法"""
        try:
            # 更新坐标轴范围
            self.ax.relim()
            self.ax.autoscale_view()

            # 使用draw_idle()进行异步绘制，提高性能
            self.canvas.draw_idle()
        except Exception as e:
            print(f"绘图更新错误: {e}")
            # 降级到完全重绘
            self.canvas.draw()

    def clear_lines(self):
        """清除所有线条"""
        for line in self.lines.values():
            line.remove()
        self.lines.clear()
        self.legend_created = False
        self.canvas.draw()


class OptimizedCombinedPlot:
    """优化的合并视图绘图 - 简化版本"""

    def __init__(self, master, fields, colors, figsize=(8, 6)):
        self.fields = fields
        self.colors = colors

        # 创建matplotlib图形
        self.fig, self.ax = plt.subplots(figsize=figsize, dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, master=master)
        self.canvas.get_tk_widget().pack(fill='both', expand=True)

        # 存储line对象
        self.lines = {}

        # 设置图形属性
        self.ax.grid(True, alpha=0.3)
        self.ax.set_xlabel("时间 (秒)")
        self.ax.set_ylabel("数值")
        self.ax.set_title("合并视图")

        # 为每个字段创建线条
        for i, field_name in enumerate(fields):
            color = colors(i % 20)
            line, = self.ax.plot([], [], label=field_name, color=color,
                               linewidth=1.5, marker='o', markersize=2, alpha=0.8)
            self.lines[field_name] = line

        # 创建图例
        if self.lines:
            self.ax.legend(loc='upper left', fontsize='small')

        self.canvas.draw()

    def update_data(self, data_history, time_data, time_window):
        """更新所有数据"""
        if not time_data:
            return

        # 计算时间窗口
        current_time = time_data[-1]
        x_min = current_time - time_window
        x_max = current_time

        # 更新每条线的数据
        has_data = False
        for field_name in self.fields:
            data = data_history.get(field_name, [])
            if data and len(data) == len(time_data) and field_name in self.lines:
                self.lines[field_name].set_data(time_data, data)
                has_data = True

        if has_data:
            # 设置坐标轴范围
            self.ax.set_xlim(x_min, x_max)
            self.ax.relim()
            self.ax.autoscale_view(scalex=False, scaley=True)

            # 更新标题显示时间窗口
            self.ax.set_title(f"合并视图 (显示最近 {time_window} 秒)")

            # 使用draw_idle()提高性能
            self.canvas.draw_idle()


class OptimizedIndividualPlot:
    """优化的独立视图绘图 - 简化版本"""

    def __init__(self, master, field_name, color, figsize=(8, 2.5)):
        self.field_name = field_name

        # 创建matplotlib图形
        self.fig, self.ax = plt.subplots(figsize=figsize, dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, master=master)
        self.canvas.get_tk_widget().pack(fill='x', expand=True, padx=5, pady=2)

        # 创建线条
        self.line, = self.ax.plot([], [], color=color,
                                 linewidth=1.5, marker='o', markersize=2, alpha=0.8)

        # 设置图形属性
        self.ax.grid(True, alpha=0.3)
        self.ax.set_xlabel("时间 (秒)")
        self.ax.set_ylabel("数值")
        self.ax.set_title(field_name)

        self.fig.tight_layout()
        self.canvas.draw()

    def update_data(self, data, time_data, time_window):
        """更新数据"""
        if not time_data or not data or len(data) != len(time_data):
            return

        # 计算时间窗口
        current_time = time_data[-1]
        x_min = current_time - time_window
        x_max = current_time

        # 更新数据
        self.line.set_data(time_data, data)

        # 设置坐标轴范围
        self.ax.set_xlim(x_min, x_max)
        self.ax.relim()
        self.ax.autoscale_view(scalex=False, scaley=True)

        # 更新标题显示时间窗口
        self.ax.set_title(f"{self.field_name} (最近 {time_window} 秒)")

        # 使用draw_idle()提高性能
        self.canvas.draw_idle()


class OptimizedSenderPlot:
    """优化的发送端绘图"""
    
    def __init__(self, master, field_name, figsize=(4, 3)):
        self.field_name = field_name
        self.plot_widget = OptimizedPlotWidget(master, figsize)
        
        # 创建单条线
        self.plot_widget.add_line(field_name, 0)
        self.plot_widget.lines[field_name].set_color('blue')
        
        self.plot_widget.ax.set_title(f"{field_name}")
        self.plot_widget.ax.set_xlabel("采样点")
    
    def update_data(self, data, current_value):
        """更新数据"""
        if not data:
            return
        
        # 创建x轴数据
        x_data = list(range(len(data)))
        
        # 更新数据
        self.plot_widget.update_line_data(self.field_name, x_data, data)
        
        # 自动调整范围
        self.plot_widget.set_ylim_auto()
        if x_data:
            self.plot_widget.ax.set_xlim(0, max(x_data))
        
        # 更新标题显示当前值
        self.plot_widget.ax.set_title(f"{self.field_name}: {current_value:.2f}")
        
        # 高效绘制
        self.plot_widget.draw_optimized()


# 数据缓存优化类
class DataBuffer:
    """高效的数据缓存，使用deque提高性能"""
    
    def __init__(self, maxlen=1000):
        self.maxlen = maxlen
        self.buffers = {}
    
    def add_field(self, field_name):
        """添加新字段"""
        if field_name not in self.buffers:
            self.buffers[field_name] = deque(maxlen=self.maxlen)
    
    def append_data(self, field_name, value):
        """添加数据点"""
        if field_name in self.buffers:
            self.buffers[field_name].append(value)
    
    def get_data(self, field_name):
        """获取数据"""
        return list(self.buffers.get(field_name, []))
    
    def clear(self):
        """清除所有数据"""
        for buffer in self.buffers.values():
            buffer.clear()
    
    def clear_field(self, field_name):
        """清除指定字段数据"""
        if field_name in self.buffers:
            self.buffers[field_name].clear()
