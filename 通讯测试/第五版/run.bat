@echo off
chcp 65001 >nul

echo 正在启动442通讯发送端和接收端...

REM 切换到当前脚本所在的目录
cd /d "%~dp0"

echo 当前目录: %cd%
echo 正在激活 Conda 环境: HGDhtml2...

REM 使用 CALL 来执行 conda.bat, 这样可以保证环境在当前窗口被激活
CALL conda.bat activate HGDhtml2

echo 正在启动接收端...
echo 正在启动发送端...

start "422 Receiver" cmd /c "conda activate %CONDA_ENV_NAME% && python 442接收端_2.py"
start "422 Sender" cmd /c "conda activate %CONDA_ENV_NAME% && python 442通讯发送端_2.py"
start "422 manager" cmd /c "conda activate %CONDA_ENV_NAME% && python protocol_manager.py"

echo 442通讯测试完成！


