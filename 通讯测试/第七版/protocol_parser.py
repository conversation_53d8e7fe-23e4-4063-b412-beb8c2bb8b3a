# import csv
# import os
#
#
# class ProtocolParser:
#     """
#     从CSV文件加载、保存和解析协议定义。
#     内部使用0-based索引，CSV文件使用1-based索引。
#     """
#
#     def __init__(self):
#         self.protocol_structure = []
#         self._build_byte_map()
#
#     def load_from_csv(self, file_path):
#         """从CSV文件加载协议定义。"""
#         structure = []
#         try:
#             with open(file_path, 'r', encoding='utf-8-sig') as f:
#                 reader = csv.reader(f)
#                 header = next(reader)
#
#                 expected_header = ["字节序号", "名称", "单位", "数据类型/长度"]
#                 if header[:len(expected_header)] != expected_header:
#                     print(f"警告: CSV文件头 '{header}' 与预期 '{expected_header}' 不符。")
#
#                 for row in reader:
#                     if not row or len(row) < 4: continue
#
#                     byte_range_str, name, unit, type_str = row[0], row[1], row[2], row[3]
#                     description = row[4] if len(row) > 4 else ""
#
#                     name = name.strip()
#                     type_str = type_str.strip().lower()
#
#                     # 解析字节范围 (CSV是1-based), 并转换为程序使用的0-based索引
#                     if '-' in byte_range_str:
#                         start, end = map(int, byte_range_str.split('-'))
#                         byte_indices = list(range(start - 1, end))
#                     else:
#                         start = int(byte_range_str)
#                         byte_indices = [start - 1]
#
#                     length = len(byte_indices)
#
#                     field_def = {
#                         'name': name,
#                         'byte_indices': byte_indices,
#                         'length': length,
#                         'type': type_str,
#                         'unit': unit,
#                         'description': description
#                     }
#                     structure.append(field_def)
#
#             self.protocol_structure = structure
#             self._build_byte_map()
#             print(f"成功从 {os.path.basename(file_path)} 加载了 {len(self.protocol_structure)} 个协议字段。")
#             return True
#         except Exception as e:
#             print(f"加载协议文件 '{file_path}' 失败: {e}")
#             self.protocol_structure = []
#             return False
#
#     def save_to_csv(self, file_path):
#         """将当前协议结构保存到CSV文件。"""
#         try:
#             with open(file_path, 'w', encoding='utf-8', newline='') as f:
#                 writer = csv.writer(f)
#                 writer.writerow(['字节序号', '名称', '单位', '数据类型/长度', '描述'])
#
#                 for field in self.protocol_structure:
#                     byte_indices = field.get('byte_indices', [])
#                     if not byte_indices: continue
#
#                     # 将0-based索引转换回1-based写入CSV
#                     if len(byte_indices) == 1:
#                         byte_idx_str = str(byte_indices[0] + 1)
#                     else:
#                         byte_idx_str = f"{byte_indices[0] + 1}-{byte_indices[-1] + 1}"
#
#                     writer.writerow([
#                         byte_idx_str,
#                         field.get('name', ''),
#                         field.get('unit', ''),
#                         field.get('type', ''),
#                         field.get('description', '')
#                     ])
#             return True
#         except Exception as e:
#             print(f"保存协议文件错误: {e}")
#             return False
#
#     def _build_byte_map(self):
#         """(内部函数) 重建字节到字段的映射，用于快速查找。"""
#         self.byte_to_field_map = {}
#         for field in self.protocol_structure:
#             for idx in field.get('byte_indices', []):
#                 self.byte_to_field_map[idx] = field
#
#     def get_protocol_structure(self):
#         """获取解析后的协议结构列表。"""
#         return self.protocol_structure
#
#     def get_field_by_name(self, name):
#         """通过名称获取字段定义。"""
#         for field in self.protocol_structure:
#             if field.get('name') == name:
#                 return field
#         return None

import csv
import os

class ProtocolParser:
    """
    从CSV文件加载、保存和解析协议定义。
    内部使用0-based索引，CSV文件使用1-based索引。
    """

    def __init__(self):
        self.protocol_structure = []
        self._build_byte_map()

    def load_from_csv(self, file_path):
        """从CSV文件加载协议定义。"""
        structure = []
        try:
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.reader(f)
                header = next(reader)

                expected_header = ["字节序号", "名称", "单位", "数据类型/长度"]
                if header[:len(expected_header)] != expected_header:
                    print(f"警告: CSV文件头 '{header}' 与预期 '{expected_header}' 不符。")

                for row in reader:
                    if not row or len(row) < 4: continue

                    byte_range_str, name, unit, type_str = row[0], row[1], row[2], row[3]
                    description = row[4] if len(row) > 4 else ""

                    name = name.strip()
                    type_str = type_str.strip().lower()

                    # !! 修正：智能识别 "2xuint8" 或 "2×uint8" 并将其视为 uint16 !!
                    if '2×uint8' in type_str or '2xuint8' in type_str:
                        type_str = 'uint16'

                    # 解析字节范围 (CSV是1-based), 并转换为程序使用的0-based索引
                    if '-' in byte_range_str:
                        start, end = map(int, byte_range_str.split('-'))
                        byte_indices = list(range(start - 1, end))
                    else:
                        start = int(byte_range_str)
                        byte_indices = [start - 1]

                    length = len(byte_indices)

                    field_def = {
                        'name': name,
                        'byte_indices': byte_indices,
                        'length': length,
                        'type': type_str,
                        'unit': unit,
                        'description': description
                    }
                    structure.append(field_def)

            self.protocol_structure = structure
            self._build_byte_map()
            print(f"成功从 {os.path.basename(file_path)} 加载了 {len(self.protocol_structure)} 个协议字段。")
            return True
        except Exception as e:
            print(f"加载协议文件 '{file_path}' 失败: {e}")
            self.protocol_structure = []
            return False

    def save_to_csv(self, file_path):
        """将当前协议结构保存到CSV文件。"""
        try:
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['字节序号', '名称', '单位', '数据类型/长度', '描述'])

                for field in self.protocol_structure:
                    byte_indices = field.get('byte_indices', [])
                    if not byte_indices: continue

                    if len(byte_indices) == 1:
                        byte_idx_str = str(byte_indices[0] + 1)
                    else:
                        byte_idx_str = f"{byte_indices[0] + 1}-{byte_indices[-1] + 1}"

                    writer.writerow([
                        byte_idx_str,
                        field.get('name', ''),
                        field.get('unit', ''),
                        field.get('type', ''),
                        field.get('description', '')
                    ])
            return True
        except Exception as e:
            print(f"保存协议文件错误: {e}")
            return False

    def _build_byte_map(self):
        """(内部函数) 重建字节到字段的映射，用于快速查找。"""
        self.byte_to_field_map = {}
        for field in self.protocol_structure:
            for idx in field.get('byte_indices', []):
                self.byte_to_field_map[idx] = field

    def get_protocol_structure(self):
        """获取解析后的协议结构列表。"""
        return self.protocol_structure

    def get_field_by_name(self, name):
        """通过名称获取字段定义。"""
        for field in self.protocol_structure:
            if field.get('name') == name:
                return field
        return None